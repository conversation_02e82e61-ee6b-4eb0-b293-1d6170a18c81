using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Cluster;
using ElisticSearch.Configuration;
using ElisticSearch.Models;
using Microsoft.Extensions.Options;

namespace ElisticSearch.Services
{
    public class ShardManagementService : IShardManagementService
    {
        private readonly ElasticsearchClient _client;
        private readonly ElasticsSettings _settings;

        public ShardManagementService(IOptions<ElasticsSettings> settings)
        {
            _settings = settings.Value;
            var clientSettings = new ElasticsearchClientSettings(new Uri(_settings.Url!))
                .Authentication(new ApiKey(_settings.ApiKey))
                .DefaultIndex(_settings.DefaultIndex!);

            _client = new ElasticsearchClient(clientSettings);
        }

        public async Task<bool> ConfigureIndexShardsAsync(string indexName, int numberOfShards, int numberOfReplicas)
        {
            try
            {
                // Note: Number of shards cannot be changed after index creation
                // Only replicas can be updated
                var settings = new Dictionary<string, object>
                {
                    ["number_of_replicas"] = numberOfReplicas
                };

                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdateReplicaCountAsync(string indexName, int numberOfReplicas)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["number_of_replicas"] = numberOfReplicas
                };

                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<ShardInfo> GetShardInfoAsync(string indexName)
        {
            try
            {
                var response = await _client.Cat.ShardsAsync(c => c
                    .Index(indexName)
                    .Format("json"));

                if (response.IsValidResponse && response.Records != null && response.Records.Any())
                {
                    var shard = response.Records.First();
                    return new ShardInfo
                    {
                        IndexName = shard.Index ?? indexName,
                        ShardNumber = int.TryParse(shard.Shard, out var shardNum) ? shardNum : 0,
                        IsPrimary = shard.Prirep == "p",
                        State = shard.State ?? "unknown",
                        NodeName = shard.Node ?? "unknown",
                        DocumentCount = long.TryParse(shard.Docs, out var docs) ? docs : 0,
                        Size = shard.Store ?? "0b"
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new ShardInfo { IndexName = indexName };
        }

        public async Task<List<ShardInfo>> GetAllShardsInfoAsync()
        {
            try
            {
                var response = await _client.Cat.ShardsAsync(c => c.Format("json"));
                var shards = new List<ShardInfo>();

                if (response.IsValidResponse && response.Records != null)
                {
                    foreach (var shard in response.Records)
                    {
                        shards.Add(new ShardInfo
                        {
                            IndexName = shard.Index ?? "unknown",
                            ShardNumber = int.TryParse(shard.Shard, out var shardNum) ? shardNum : 0,
                            IsPrimary = shard.Prirep == "p",
                            State = shard.State ?? "unknown",
                            NodeName = shard.Node ?? "unknown",
                            DocumentCount = long.TryParse(shard.Docs, out var docs) ? docs : 0,
                            Size = shard.Store ?? "0b"
                        });
                    }
                }

                return shards;
            }
            catch (Exception)
            {
                return new List<ShardInfo>();
            }
        }

        public async Task<bool> SetShardAllocationSettingsAsync(Dictionary<string, object> settings)
        {
            try
            {
                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetShardAllocationSettingsAsync()
        {
            try
            {
                var response = await _client.Cluster.GetSettingsAsync();
                if (response.IsValidResponse)
                {
                    var settings = new Dictionary<string, object>();
                    
                    if (response.Persistent != null)
                    {
                        foreach (var setting in response.Persistent)
                        {
                            settings[$"persistent.{setting.Key}"] = setting.Value;
                        }
                    }

                    if (response.Transient != null)
                    {
                        foreach (var setting in response.Transient)
                        {
                            settings[$"transient.{setting.Key}"] = setting.Value;
                        }
                    }

                    return settings;
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new Dictionary<string, object>();
        }

        public async Task<bool> EnableShardAllocationAsync()
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["cluster.routing.allocation.enable"] = "all"
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DisableShardAllocationAsync()
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["cluster.routing.allocation.enable"] = "none"
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> SetShardRoutingAsync(string indexName, string routingValue)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["routing.allocation.require._name"] = routingValue
                };

                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> RebalanceClusterAsync()
        {
            try
            {
                var response = await _client.Cluster.RerouteAsync(r => r
                    .RetryFailed(true));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> RebalanceIndexAsync(string indexName)
        {
            try
            {
                // Force rebalancing by temporarily disabling and re-enabling allocation
                await DisableShardAllocationAsync();
                await Task.Delay(1000); // Brief pause
                await EnableShardAllocationAsync();
                
                var response = await _client.Cluster.RerouteAsync(r => r
                    .RetryFailed(true));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> MoveShardAsync(string indexName, int shardNumber, string fromNode, string toNode)
        {
            try
            {
                var response = await _client.Cluster.RerouteAsync(r => r
                    .Commands(c => c
                        .Move(m => m
                            .Index(indexName)
                            .Shard(shardNumber)
                            .FromNode(fromNode)
                            .ToNode(toNode))));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> CancelShardMovementAsync(string indexName, int shardNumber)
        {
            try
            {
                var response = await _client.Cluster.RerouteAsync(r => r
                    .Commands(c => c
                        .Cancel(cancel => cancel
                            .Index(indexName)
                            .Shard(shardNumber)
                            .AllowPrimary(false))));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<ShardStatus>> GetShardStatusAsync()
        {
            try
            {
                var response = await _client.Cat.ShardsAsync(c => c.Format("json"));
                var shardStatuses = new List<ShardStatus>();

                if (response.IsValidResponse && response.Records != null)
                {
                    foreach (var shard in response.Records)
                    {
                        shardStatuses.Add(new ShardStatus
                        {
                            IndexName = shard.Index ?? "unknown",
                            ShardNumber = int.TryParse(shard.Shard, out var shardNum) ? shardNum : 0,
                            IsPrimary = shard.Prirep == "p",
                            State = shard.State ?? "unknown",
                            NodeName = shard.Node ?? "unassigned",
                            UnassignedReason = shard.State == "UNASSIGNED" ? "Unknown" : string.Empty
                        });
                    }
                }

                return shardStatuses;
            }
            catch (Exception)
            {
                return new List<ShardStatus>();
            }
        }

        public async Task<List<ShardStatus>> GetUnassignedShardsAsync()
        {
            try
            {
                var allShards = await GetShardStatusAsync();
                return allShards.Where(s => s.State.Equals("UNASSIGNED", StringComparison.OrdinalIgnoreCase)).ToList();
            }
            catch (Exception)
            {
                return new List<ShardStatus>();
            }
        }

        public async Task<List<ShardStatus>> GetRelocatingShardsAsync()
        {
            try
            {
                var allShards = await GetShardStatusAsync();
                return allShards.Where(s => s.State.Equals("RELOCATING", StringComparison.OrdinalIgnoreCase)).ToList();
            }
            catch (Exception)
            {
                return new List<ShardStatus>();
            }
        }

        public async Task<ShardAllocationExplanation> ExplainShardAllocationAsync(string indexName, int shardNumber)
        {
            try
            {
                var response = await _client.Cluster.AllocationExplainAsync(a => a
                    .Index(indexName)
                    .Shard(shardNumber)
                    .Primary(true));

                if (response.IsValidResponse)
                {
                    return new ShardAllocationExplanation
                    {
                        IndexName = indexName,
                        ShardNumber = shardNumber,
                        IsPrimary = true,
                        CurrentState = response.CurrentState ?? "unknown",
                        CanAllocate = response.CanAllocate ?? false,
                        CanRemainOnCurrentNode = response.CanRemainOnCurrentNode ?? false,
                        Explanation = "Allocation explanation retrieved successfully"
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new ShardAllocationExplanation
            {
                IndexName = indexName,
                ShardNumber = shardNumber,
                Explanation = "Failed to retrieve allocation explanation"
            };
        }

        public async Task<List<NodeInfo>> GetNodesInfoAsync()
        {
            try
            {
                var response = await _client.Nodes.InfoAsync();
                var nodes = new List<NodeInfo>();

                if (response.IsValidResponse && response.Nodes != null)
                {
                    foreach (var node in response.Nodes)
                    {
                        nodes.Add(new NodeInfo
                        {
                            NodeId = node.Key,
                            NodeName = node.Value.Name ?? "unknown",
                            Host = node.Value.Host ?? "unknown",
                            Ip = node.Value.Ip ?? "unknown",
                            Roles = node.Value.Roles?.ToList() ?? new List<string>(),
                            Attributes = node.Value.Attributes?.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value)
                                        ?? new Dictionary<string, object>()
                        });
                    }
                }

                return nodes;
            }
            catch (Exception)
            {
                return new List<NodeInfo>();
            }
        }

        public async Task<bool> ExcludeNodeFromAllocationAsync(string nodeId)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["cluster.routing.allocation.exclude._id"] = nodeId
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> IncludeNodeInAllocationAsync(string nodeId)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["cluster.routing.allocation.exclude._id"] = null // Remove exclusion
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetNodeAllocationSettingsAsync(string nodeId)
        {
            try
            {
                var response = await _client.Cluster.GetSettingsAsync();
                var nodeSettings = new Dictionary<string, object>();

                if (response.IsValidResponse)
                {
                    // Extract node-specific allocation settings
                    if (response.Persistent != null)
                    {
                        foreach (var setting in response.Persistent)
                        {
                            if (setting.Key.Contains("allocation") || setting.Key.Contains(nodeId))
                            {
                                nodeSettings[setting.Key] = setting.Value;
                            }
                        }
                    }
                }

                return nodeSettings;
            }
            catch (Exception)
            {
                return new Dictionary<string, object>();
            }
        }

        public async Task<bool> SetClusterSettingsAsync(Dictionary<string, object> persistentSettings, Dictionary<string, object> transientSettings)
        {
            try
            {
                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(persistentSettings)
                    .Transient(transientSettings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetClusterSettingsAsync()
        {
            try
            {
                var response = await _client.Cluster.GetSettingsAsync();
                var allSettings = new Dictionary<string, object>();

                if (response.IsValidResponse)
                {
                    if (response.Persistent != null)
                    {
                        foreach (var setting in response.Persistent)
                        {
                            allSettings[$"persistent.{setting.Key}"] = setting.Value;
                        }
                    }

                    if (response.Transient != null)
                    {
                        foreach (var setting in response.Transient)
                        {
                            allSettings[$"transient.{setting.Key}"] = setting.Value;
                        }
                    }

                    if (response.Defaults != null)
                    {
                        foreach (var setting in response.Defaults)
                        {
                            allSettings[$"default.{setting.Key}"] = setting.Value;
                        }
                    }
                }

                return allSettings;
            }
            catch (Exception)
            {
                return new Dictionary<string, object>();
            }
        }

        public async Task<bool> SetShardAllocationAwarenessAsync(string[] attributes)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["cluster.routing.allocation.awareness.attributes"] = string.Join(",", attributes)
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> SetShardAllocationFilteringAsync(string filterType, string[] values)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    [$"cluster.routing.allocation.{filterType}._name"] = string.Join(",", values)
                };

                var response = await _client.Cluster.PutSettingsAsync(s => s
                    .Persistent(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
