using ElisticSearch.Models;

namespace ElisticSearch.Services
{
    public interface IShardManagementService
    {
        // Shard Configuration
        Task<bool> ConfigureIndexShardsAsync(string indexName, int numberOfShards, int numberOfReplicas);
        Task<bool> UpdateReplicaCountAsync(string indexName, int numberOfReplicas);
        Task<ShardInfo> GetShardInfoAsync(string indexName);
        Task<List<ShardInfo>> GetAllShardsInfoAsync();

        // Shard Allocation
        Task<bool> SetShardAllocationSettingsAsync(Dictionary<string, object> settings);
        Task<Dictionary<string, object>> GetShardAllocationSettingsAsync();
        Task<bool> EnableShardAllocationAsync();
        Task<bool> DisableShardAllocationAsync();
        Task<bool> SetShardRoutingAsync(string indexName, string routingValue);

        // Shard Rebalancing
        Task<bool> RebalanceClusterAsync();
        Task<bool> RebalanceIndexAsync(string indexName);
        Task<bool> MoveShardAsync(string indexName, int shardNumber, string fromNode, string toNode);
        Task<bool> CancelShardMovementAsync(string indexName, int shardNumber);

        // Shard Monitoring
        Task<List<ShardStatus>> GetShardStatusAsync();
        Task<List<ShardStatus>> GetUnassignedShardsAsync();
        Task<List<ShardStatus>> GetRelocatingShardsAsync();
        Task<ShardAllocationExplanation> ExplainShardAllocationAsync(string indexName, int shardNumber);

        // Node Management
        Task<List<NodeInfo>> GetNodesInfoAsync();
        Task<bool> ExcludeNodeFromAllocationAsync(string nodeId);
        Task<bool> IncludeNodeInAllocationAsync(string nodeId);
        Task<Dictionary<string, object>> GetNodeAllocationSettingsAsync(string nodeId);

        // Cluster Settings
        Task<bool> SetClusterSettingsAsync(Dictionary<string, object> persistentSettings, Dictionary<string, object> transientSettings);
        Task<Dictionary<string, object>> GetClusterSettingsAsync();
        Task<bool> SetShardAllocationAwarenessAsync(string[] attributes);
        Task<bool> SetShardAllocationFilteringAsync(string filterType, string[] values);
    }

    // Supporting Models
    public class ShardInfo
    {
        public string IndexName { get; set; } = string.Empty;
        public int ShardNumber { get; set; }
        public bool IsPrimary { get; set; }
        public string State { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public long DocumentCount { get; set; }
        public string Size { get; set; } = string.Empty;
        public string RoutingValue { get; set; } = string.Empty;
    }

    public class ShardStatus
    {
        public string IndexName { get; set; } = string.Empty;
        public int ShardNumber { get; set; }
        public bool IsPrimary { get; set; }
        public string State { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public string UnassignedReason { get; set; } = string.Empty;
        public DateTime? AllocationTime { get; set; }
        public string RecoverySource { get; set; } = string.Empty;
    }

    public class ShardAllocationExplanation
    {
        public string IndexName { get; set; } = string.Empty;
        public int ShardNumber { get; set; }
        public bool IsPrimary { get; set; }
        public string CurrentState { get; set; } = string.Empty;
        public bool CanAllocate { get; set; }
        public bool CanRemainOnCurrentNode { get; set; }
        public List<AllocationDecision> AllocationDecisions { get; set; } = new();
        public string Explanation { get; set; } = string.Empty;
    }

    public class AllocationDecision
    {
        public string NodeName { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string Decision { get; set; } = string.Empty;
        public string Explanation { get; set; } = string.Empty;
        public int Weight { get; set; }
    }

    public class NodeInfo
    {
        public string NodeId { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public string Host { get; set; } = string.Empty;
        public string Ip { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new();
        public Dictionary<string, object> Attributes { get; set; } = new();
        public NodeStats Stats { get; set; } = new();
        public bool IsExcludedFromAllocation { get; set; }
    }

    public class NodeStats
    {
        public long TotalShards { get; set; }
        public long PrimaryShards { get; set; }
        public long ReplicaShards { get; set; }
        public string DiskUsed { get; set; } = string.Empty;
        public string DiskAvailable { get; set; } = string.Empty;
        public double DiskUsedPercent { get; set; }
        public string MemoryUsed { get; set; } = string.Empty;
        public string MemoryTotal { get; set; } = string.Empty;
        public double CpuUsage { get; set; }
    }
}
