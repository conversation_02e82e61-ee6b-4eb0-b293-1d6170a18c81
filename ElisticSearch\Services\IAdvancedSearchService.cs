using ElisticSearch.Models;

namespace ElisticSearch.Services
{
    public interface IAdvancedSearchService
    {
        // Basic Search
        Task<SearchResult<T>> SearchAsync<T>(string indexName, SearchRequest request) where T : class;
        Task<SearchResult<T>> FullTextSearchAsync<T>(string indexName, string query, int from = 0, int size = 10) where T : class;
        Task<SearchResult<T>> MatchQueryAsync<T>(string indexName, string field, string value, int from = 0, int size = 10) where T : class;
        Task<SearchResult<T>> TermQueryAsync<T>(string indexName, string field, object value, int from = 0, int size = 10) where T : class;

        // Advanced Queries
        Task<SearchResult<T>> BoolQueryAsync<T>(string indexName, BoolQueryRequest request) where T : class;
        Task<SearchResult<T>> RangeQueryAsync<T>(string indexName, string field, object from, object to, int skip = 0, int take = 10) where T : class;
        Task<SearchResult<T>> WildcardQueryAsync<T>(string indexName, string field, string pattern, int from = 0, int size = 10) where T : class;
        Task<SearchResult<T>> FuzzyQueryAsync<T>(string indexName, string field, string value, int fuzziness = 1, int from = 0, int size = 10) where T : class;

        // Filtering
        Task<SearchResult<T>> FilteredSearchAsync<T>(string indexName, SearchRequest searchRequest, List<FilterRequest> filters) where T : class;
        Task<SearchResult<T>> MultiFieldSearchAsync<T>(string indexName, Dictionary<string, object> fieldValues, int from = 0, int size = 10) where T : class;

        // Aggregations
        Task<AggregationResult> GetAggregationsAsync(string indexName, List<AggregationRequest> aggregations, SearchRequest? searchRequest = null);
        Task<AggregationResult> GetTermsAggregationAsync(string indexName, string field, int size = 10);
        Task<AggregationResult> GetDateHistogramAsync(string indexName, string field, string interval);
        Task<AggregationResult> GetStatsAggregationAsync(string indexName, string field);

        // Suggestions
        Task<List<SuggestionResult>> GetSuggestionsAsync(string indexName, string text, string field, int size = 5);
        Task<List<SuggestionResult>> GetCompletionSuggestionsAsync(string indexName, string text, string field, int size = 5);

        // Highlighting
        Task<SearchResult<T>> SearchWithHighlightAsync<T>(string indexName, SearchRequest request, HighlightRequest highlight) where T : class;

        // Sorting
        Task<SearchResult<T>> SearchWithSortAsync<T>(string indexName, SearchRequest request, List<SortRequest> sorts) where T : class;

        // Scroll API for large result sets
        Task<ScrollResult<T>> InitiateScrollAsync<T>(string indexName, SearchRequest request, string scrollTime = "1m") where T : class;
        Task<ScrollResult<T>> ContinueScrollAsync<T>(string scrollId, string scrollTime = "1m") where T : class;
        Task<bool> ClearScrollAsync(string scrollId);

        // More Like This
        Task<SearchResult<T>> MoreLikeThisAsync<T>(string indexName, string documentId, List<string> fields, int size = 10) where T : class;

        // Percolate (Reverse Search)
        Task<bool> RegisterPercolateQueryAsync(string indexName, string queryId, object query);
        Task<List<string>> PercolateAsync(string indexName, object document);

        // Search Templates
        Task<bool> CreateSearchTemplateAsync(string templateId, string template);
        Task<SearchResult<T>> SearchWithTemplateAsync<T>(string indexName, string templateId, Dictionary<string, object> parameters) where T : class;
    }

    // Supporting Models for Advanced Search
    public class BoolQueryRequest
    {
        public List<object> Must { get; set; } = new();
        public List<object> Should { get; set; } = new();
        public List<object> MustNot { get; set; } = new();
        public List<object> Filter { get; set; } = new();
        public int MinimumShouldMatch { get; set; } = 0;
    }

    public class FilterRequest
    {
        public string Type { get; set; } = string.Empty; // term, range, exists, etc.
        public string Field { get; set; } = string.Empty;
        public object Value { get; set; } = new();
        public object From { get; set; } = new();
        public object To { get; set; } = new();
    }

    public class AggregationRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // terms, date_histogram, stats, etc.
        public string Field { get; set; } = string.Empty;
        public int Size { get; set; } = 10;
        public string Interval { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class AggregationResult
    {
        public Dictionary<string, object> Aggregations { get; set; } = new();
        public long TotalHits { get; set; }
    }

    public class SuggestionResult
    {
        public string Text { get; set; } = string.Empty;
        public double Score { get; set; }
        public Dictionary<string, object> Options { get; set; } = new();
    }

    public class HighlightRequest
    {
        public List<string> Fields { get; set; } = new();
        public string PreTag { get; set; } = "<em>";
        public string PostTag { get; set; } = "</em>";
        public int FragmentSize { get; set; } = 150;
        public int NumberOfFragments { get; set; } = 3;
    }

    public class SortRequest
    {
        public string Field { get; set; } = string.Empty;
        public string Order { get; set; } = "asc"; // asc or desc
        public string Mode { get; set; } = string.Empty; // min, max, sum, avg, median
        public object Missing { get; set; } = "_last"; // _first, _last, or custom value
    }

    public class ScrollResult<T>
    {
        public string ScrollId { get; set; } = string.Empty;
        public List<T> Documents { get; set; } = new();
        public long TotalHits { get; set; }
        public bool HasMore { get; set; }
        public TimeSpan Took { get; set; }
    }

    public class SearchHit<T>
    {
        public T Source { get; set; } = default!;
        public double Score { get; set; }
        public Dictionary<string, List<string>> Highlight { get; set; } = new();
        public Dictionary<string, object> Fields { get; set; } = new();
        public string Index { get; set; } = string.Empty;
        public string Id { get; set; } = string.Empty;
    }

    public class QueryBuilder
    {
        public static object MatchQuery(string field, string value)
        {
            return new { match = new Dictionary<string, object> { [field] = value } };
        }

        public static object TermQuery(string field, object value)
        {
            return new { term = new Dictionary<string, object> { [field] = value } };
        }

        public static object RangeQuery(string field, object from, object to)
        {
            return new { range = new Dictionary<string, object> { [field] = new { from, to } } };
        }

        public static object BoolQuery(List<object> must = null, List<object> should = null, List<object> mustNot = null, List<object> filter = null)
        {
            var boolQuery = new Dictionary<string, object>();
            if (must?.Any() == true) boolQuery["must"] = must;
            if (should?.Any() == true) boolQuery["should"] = should;
            if (mustNot?.Any() == true) boolQuery["must_not"] = mustNot;
            if (filter?.Any() == true) boolQuery["filter"] = filter;
            
            return new { @bool = boolQuery };
        }

        public static object WildcardQuery(string field, string pattern)
        {
            return new { wildcard = new Dictionary<string, object> { [field] = pattern } };
        }

        public static object FuzzyQuery(string field, string value, int fuzziness = 1)
        {
            return new { fuzzy = new Dictionary<string, object> { [field] = new { value, fuzziness } } };
        }
    }
}
