using ElisticSearch.Models;
using ElisticSearch.Services;
using Microsoft.AspNetCore.Mvc;

namespace ElisticSearch.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SchemaController : ControllerBase
    {
        private readonly ISchemaManagementService _schemaService;
        private readonly ILogger<SchemaController> _logger;

        public SchemaController(ISchemaManagementService schemaService, ILogger<SchemaController> logger)
        {
            _schemaService = schemaService;
            _logger = logger;
        }

        [HttpPost("create-index")]
        public async Task<IActionResult> CreateIndex([FromBody] CreateIndexRequest request)
        {
            try
            {
                var schema = new IndexSchema
                {
                    IndexName = request.IndexName,
                    NumberOfShards = request.NumberOfShards,
                    NumberOfReplicas = request.NumberOfReplicas,
                    RefreshInterval = request.RefreshInterval ?? "1s",
                    Settings = request.Settings ?? new Dictionary<string, object>(),
                    Mappings = request.Mappings ?? new Dictionary<string, Elastic.Clients.Elasticsearch.Mapping.IProperty>(),
                    Aliases = request.Aliases ?? new List<string>()
                };

                var result = await _schemaService.CreateIndexWithSchemaAsync(request.IndexName, schema);
                return result ? Ok($"Index {request.IndexName} created successfully.") 
                             : StatusCode(500, "Failed to create index.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating index {IndexName}", request.IndexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("index/{indexName}/stats")]
        public async Task<IActionResult> GetIndexStats(string indexName)
        {
            try
            {
                var stats = await _schemaService.GetIndexStatsAsync(indexName);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stats for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("indices/stats")]
        public async Task<IActionResult> GetAllIndicesStats()
        {
            try
            {
                var stats = await _schemaService.GetAllIndicesStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all indices stats");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("index/{indexName}/mapping")]
        public async Task<IActionResult> UpdateMapping(string indexName, [FromBody] Dictionary<string, object> mappings)
        {
            try
            {
                // Convert mappings to IProperty dictionary (simplified)
                var propertyMappings = new Dictionary<string, Elastic.Clients.Elasticsearch.Mapping.IProperty>();
                var result = await _schemaService.UpdateIndexMappingAsync(indexName, propertyMappings);
                return result ? Ok($"Mapping updated for index {indexName}.") 
                             : StatusCode(500, "Failed to update mapping.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating mapping for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("index/{indexName}/mapping")]
        public async Task<IActionResult> GetMapping(string indexName)
        {
            try
            {
                var mapping = await _schemaService.GetIndexMappingAsync(indexName);
                return Ok(mapping);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mapping for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("alias")]
        public async Task<IActionResult> CreateAlias([FromBody] CreateAliasRequest request)
        {
            try
            {
                var result = await _schemaService.CreateAliasAsync(request.IndexName, request.AliasName);
                return result ? Ok($"Alias {request.AliasName} created for index {request.IndexName}.") 
                             : StatusCode(500, "Failed to create alias.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating alias {AliasName} for index {IndexName}", 
                    request.AliasName, request.IndexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpDelete("alias")]
        public async Task<IActionResult> DeleteAlias([FromQuery] string indexName, [FromQuery] string aliasName)
        {
            try
            {
                var result = await _schemaService.DeleteAliasAsync(indexName, aliasName);
                return result ? Ok($"Alias {aliasName} deleted from index {indexName}.") 
                             : StatusCode(500, "Failed to delete alias.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting alias {AliasName} from index {IndexName}", 
                    aliasName, indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("aliases")]
        public async Task<IActionResult> GetAliases()
        {
            try
            {
                var aliases = await _schemaService.GetAliasesAsync();
                return Ok(aliases);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting aliases");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("template")]
        public async Task<IActionResult> CreateTemplate([FromBody] IndexTemplate template)
        {
            try
            {
                var result = await _schemaService.CreateIndexTemplateAsync(template);
                return result ? Ok($"Template {template.Name} created successfully.") 
                             : StatusCode(500, "Failed to create template.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating template {TemplateName}", template.Name);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("templates")]
        public async Task<IActionResult> GetTemplates()
        {
            try
            {
                var templates = await _schemaService.GetIndexTemplatesAsync();
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting templates");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpDelete("index/{indexName}")]
        public async Task<IActionResult> DeleteIndex(string indexName)
        {
            try
            {
                var result = await _schemaService.DeleteIndexAsync(indexName);
                return result ? Ok($"Index {indexName} deleted successfully.") 
                             : StatusCode(500, "Failed to delete index.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("cluster/health")]
        public async Task<IActionResult> GetClusterHealth()
        {
            try
            {
                var health = await _schemaService.GetClusterHealthAsync();
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cluster health");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("index/{indexName}/refresh")]
        public async Task<IActionResult> RefreshIndex(string indexName)
        {
            try
            {
                var result = await _schemaService.RefreshIndexAsync(indexName);
                return result ? Ok($"Index {indexName} refreshed successfully.") 
                             : StatusCode(500, "Failed to refresh index.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }
    }

    // Request DTOs
    public class CreateIndexRequest
    {
        public string IndexName { get; set; } = string.Empty;
        public int NumberOfShards { get; set; } = 1;
        public int NumberOfReplicas { get; set; } = 1;
        public string? RefreshInterval { get; set; }
        public Dictionary<string, object>? Settings { get; set; }
        public Dictionary<string, Elastic.Clients.Elasticsearch.Mapping.IProperty>? Mappings { get; set; }
        public List<string>? Aliases { get; set; }
    }

    public class CreateAliasRequest
    {
        public string IndexName { get; set; } = string.Empty;
        public string AliasName { get; set; } = string.Empty;
    }
}
