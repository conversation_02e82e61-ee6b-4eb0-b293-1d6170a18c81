using ElisticSearch.Models;
using Elastic.Clients.Elasticsearch.Mapping;

namespace ElisticSearch.Services
{
    public interface ISchemaManagementService
    {
        // Index Management
        Task<bool> CreateIndexWithSchemaAsync(string indexName, IndexSchema schema);
        Task<bool> UpdateIndexMappingAsync(string indexName, Dictionary<string, IProperty> mappings);
        Task<bool> DeleteIndexAsync(string indexName);
        Task<bool> IndexExistsAsync(string indexName);
        Task<IndexStats> GetIndexStatsAsync(string indexName);
        Task<List<IndexStats>> GetAllIndicesStatsAsync();

        // Schema Management
        Task<bool> CreateIndexTemplateAsync(IndexTemplate template);
        Task<bool> DeleteIndexTemplateAsync(string templateName);
        Task<List<IndexTemplate>> GetIndexTemplatesAsync();
        Task<Dictionary<string, IProperty>> GetIndexMappingAsync(string indexName);

        // Analyzer Management
        Task<bool> CreateAnalyzerAsync(string indexName, AnalyzerConfiguration analyzer);
        Task<bool> UpdateAnalyzerAsync(string indexName, AnalyzerConfiguration analyzer);
        Task<List<AnalyzerConfiguration>> GetAnalyzersAsync(string indexName);

        // Alias Management
        Task<bool> CreateAliasAsync(string indexName, string aliasName);
        Task<bool> DeleteAliasAsync(string indexName, string aliasName);
        Task<Dictionary<string, List<string>>> GetAliasesAsync();
        Task<bool> SwitchAliasAsync(string aliasName, string oldIndexName, string newIndexName);

        // Index Settings
        Task<bool> UpdateIndexSettingsAsync(string indexName, Dictionary<string, object> settings);
        Task<Dictionary<string, object>> GetIndexSettingsAsync(string indexName);

        // Shard Management
        Task<bool> ConfigureShardsAsync(string indexName, ShardConfiguration config);
        Task<ShardConfiguration> GetShardConfigurationAsync(string indexName);
        Task<bool> RebalanceShardsAsync(string indexName);
        Task<Dictionary<string, object>> GetShardAllocationAsync();

        // Health and Monitoring
        Task<ClusterHealth> GetClusterHealthAsync();
        Task<Dictionary<string, object>> GetNodeStatsAsync();
        Task<bool> RefreshIndexAsync(string indexName);
        Task<bool> FlushIndexAsync(string indexName);
    }
}
