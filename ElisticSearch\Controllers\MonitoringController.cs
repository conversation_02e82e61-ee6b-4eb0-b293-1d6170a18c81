using ElisticSearch.Models;
using ElisticSearch.Services;
using Microsoft.AspNetCore.Mvc;

namespace ElisticSearch.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MonitoringController : ControllerBase
    {
        private readonly IMonitoringService _monitoringService;
        private readonly ILogger<MonitoringController> _logger;

        public MonitoringController(IMonitoringService monitoringService, ILogger<MonitoringController> logger)
        {
            _monitoringService = monitoringService;
            _logger = logger;
        }

        [HttpGet("cluster/health")]
        public async Task<IActionResult> GetClusterHealth()
        {
            try
            {
                var health = await _monitoringService.GetClusterHealthAsync();
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cluster health");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("cluster/stats")]
        public async Task<IActionResult> GetClusterStats()
        {
            try
            {
                var stats = await _monitoringService.GetClusterStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cluster stats");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("cluster/healthy")]
        public async Task<IActionResult> IsClusterHealthy()
        {
            try
            {
                var isHealthy = await _monitoringService.IsClusterHealthyAsync();
                return Ok(new { IsHealthy = isHealthy });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cluster health status");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes/health")]
        public async Task<IActionResult> GetNodesHealth()
        {
            try
            {
                var nodesHealth = await _monitoringService.GetNodesHealthAsync();
                return Ok(nodesHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nodes health");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes/stats")]
        public async Task<IActionResult> GetNodesStats()
        {
            try
            {
                var nodesStats = await _monitoringService.GetNodesStatsAsync();
                return Ok(nodesStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nodes stats");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes/{nodeId}/stats")]
        public async Task<IActionResult> GetNodeStats(string nodeId)
        {
            try
            {
                var nodeStats = await _monitoringService.GetNodeStatsAsync(nodeId);
                return Ok(nodeStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stats for node {NodeId}", nodeId);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes/{nodeId}/info")]
        public async Task<IActionResult> GetNodeInfo(string nodeId)
        {
            try
            {
                var nodeInfo = await _monitoringService.GetNodeInfoAsync(nodeId);
                return Ok(nodeInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting info for node {NodeId}", nodeId);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes/performance")]
        public async Task<IActionResult> GetNodesPerformance()
        {
            try
            {
                var performance = await _monitoringService.GetNodesPerformanceAsync();
                return Ok(performance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nodes performance");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("indices/stats")]
        public async Task<IActionResult> GetAllIndicesStats()
        {
            try
            {
                var stats = await _monitoringService.GetAllIndicesStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all indices stats");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("indices/{indexName}/stats")]
        public async Task<IActionResult> GetIndexStats(string indexName)
        {
            try
            {
                var stats = await _monitoringService.GetIndexStatsAsync(indexName);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stats for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("indices/{indexName}/health")]
        public async Task<IActionResult> GetIndexHealth(string indexName)
        {
            try
            {
                var health = await _monitoringService.GetIndexHealthAsync(indexName);
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting health for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("indices/performance")]
        public async Task<IActionResult> GetIndexPerformanceMetrics()
        {
            try
            {
                var metrics = await _monitoringService.GetIndexPerformanceMetricsAsync();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting index performance metrics");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("performance/overall")]
        public async Task<IActionResult> GetPerformanceMetrics()
        {
            try
            {
                var metrics = await _monitoringService.GetPerformanceMetricsAsync();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance metrics");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("performance/queries")]
        public async Task<IActionResult> GetQueryPerformance()
        {
            try
            {
                var performance = await _monitoringService.GetQueryPerformanceAsync();
                return Ok(performance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting query performance");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("performance/indexing")]
        public async Task<IActionResult> GetIndexingPerformance()
        {
            try
            {
                var performance = await _monitoringService.GetIndexingPerformanceAsync();
                return Ok(performance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting indexing performance");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("performance/search-latency")]
        public async Task<IActionResult> GetSearchLatencyMetrics()
        {
            try
            {
                var latency = await _monitoringService.GetSearchLatencyMetricsAsync();
                return Ok(latency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search latency metrics");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("resources/usage")]
        public async Task<IActionResult> GetResourceUsage()
        {
            try
            {
                var usage = await _monitoringService.GetResourceUsageAsync();
                return Ok(usage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting resource usage");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("resources/disk")]
        public async Task<IActionResult> GetDiskUsage()
        {
            try
            {
                var diskUsage = await _monitoringService.GetDiskUsageAsync();
                return Ok(diskUsage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting disk usage");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("resources/memory")]
        public async Task<IActionResult> GetMemoryUsage()
        {
            try
            {
                var memoryUsage = await _monitoringService.GetMemoryUsageAsync();
                return Ok(memoryUsage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting memory usage");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("resources/cpu")]
        public async Task<IActionResult> GetCpuUsage()
        {
            try
            {
                var cpuUsage = await _monitoringService.GetCpuUsageAsync();
                return Ok(cpuUsage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CPU usage");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("alerts")]
        public async Task<IActionResult> GetActiveAlerts()
        {
            try
            {
                var alerts = await _monitoringService.GetActiveAlertsAsync();
                return Ok(alerts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active alerts");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("thresholds")]
        public async Task<IActionResult> SetThreshold([FromBody] SetThresholdRequest request)
        {
            try
            {
                var result = await _monitoringService.SetThresholdAsync(request.MetricName, request.WarningThreshold, request.CriticalThreshold);
                return result ? Ok("Threshold set successfully.") 
                             : StatusCode(500, "Failed to set threshold.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting threshold for metric {MetricName}", request.MetricName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("thresholds")]
        public async Task<IActionResult> GetThresholds()
        {
            try
            {
                var thresholds = await _monitoringService.GetThresholdsAsync();
                return Ok(thresholds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting thresholds");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("thresholds/check")]
        public async Task<IActionResult> CheckThresholds()
        {
            try
            {
                var result = await _monitoringService.CheckThresholdsAsync();
                return Ok(new { ThresholdsChecked = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking thresholds");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("metrics/historical")]
        public async Task<IActionResult> GetHistoricalMetrics([FromQuery] string metricName, [FromQuery] DateTime from, [FromQuery] DateTime to)
        {
            try
            {
                var metrics = await _monitoringService.GetHistoricalMetricsAsync(metricName, from, to);
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting historical metrics for {MetricName}", metricName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("trends/cluster")]
        public async Task<IActionResult> GetClusterTrend([FromQuery] int periodHours = 24)
        {
            try
            {
                var period = TimeSpan.FromHours(periodHours);
                var trend = await _monitoringService.GetClusterTrendAsync(period);
                return Ok(trend);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cluster trend");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("trends/indices")]
        public async Task<IActionResult> GetIndexTrends([FromQuery] int periodHours = 24)
        {
            try
            {
                var period = TimeSpan.FromHours(periodHours);
                var trends = await _monitoringService.GetIndexTrendsAsync(period);
                return Ok(trends);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting index trends");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("events/log")]
        public async Task<IActionResult> LogCustomEvent([FromBody] LogEventRequest request)
        {
            try
            {
                var result = await _monitoringService.LogCustomEventAsync(request.EventType, request.Message, request.Metadata);
                return result ? Ok("Event logged successfully.") 
                             : StatusCode(500, "Failed to log event.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging custom event");
                return StatusCode(500, "Internal server error.");
            }
        }
    }

    // Request DTOs
    public class SetThresholdRequest
    {
        public string MetricName { get; set; } = string.Empty;
        public double WarningThreshold { get; set; }
        public double CriticalThreshold { get; set; }
    }

    public class LogEventRequest
    {
        public string EventType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
