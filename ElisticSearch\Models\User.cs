using Elastic.Clients.Elasticsearch.Mapping;

namespace ElisticSearch.Models
{
    public class User
    {
        [PropertyName("id")]
        public int Id { get; set; }

        [PropertyName("first_name")]
        [Text(Analyzer = "standard")]
        public string? FirstName { get; set; }

        [PropertyName("last_name")]
        [Text(Analyzer = "standard")]
        public string? LastName { get; set; }

        [PropertyName("email")]
        [Keyword]
        public string? Email { get; set; }

        [PropertyName("age")]
        [Number(NumberType.Integer)]
        public int? Age { get; set; }

        [PropertyName("created_at")]
        [Date]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [PropertyName("is_active")]
        [Boolean]
        public bool IsActive { get; set; } = true;

        [PropertyName("tags")]
        [Keyword]
        public List<string>? Tags { get; set; }

        [PropertyName("profile")]
        [Object]
        public UserProfile? Profile { get; set; }
    }

    public class UserProfile
    {
        [PropertyName("bio")]
        [Text(Analyzer = "standard")]
        public string? Bio { get; set; }

        [PropertyName("location")]
        [Keyword]
        public string? Location { get; set; }

        [PropertyName("website")]
        [Keyword]
        public string? Website { get; set; }
    }
}
