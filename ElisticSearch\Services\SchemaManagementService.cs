using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Elastic.Clients.Elasticsearch.Mapping;
using ElisticSearch.Configuration;
using ElisticSearch.Models;
using Microsoft.Extensions.Options;

namespace ElisticSearch.Services
{
    public class SchemaManagementService : ISchemaManagementService
    {
        private readonly ElasticsearchClient _client;
        private readonly ElasticsSettings _settings;

        public SchemaManagementService(IOptions<ElasticsSettings> settings)
        {
            _settings = settings.Value;
            var clientSettings = new ElasticsearchClientSettings(new Uri(_settings.Url!))
                .Authentication(new ApiKey(_settings.ApiKey))
                .DefaultIndex(_settings.DefaultIndex!);

            _client = new ElasticsearchClient(clientSettings);
        }

        public async Task<bool> CreateIndexWithSchemaAsync(string indexName, IndexSchema schema)
        {
            try
            {
                var response = await _client.Indices.CreateAsync(indexName, c => c
                    .Settings(s => s
                        .NumberOfShards(schema.NumberOfShards)
                        .NumberOfReplicas(schema.NumberOfReplicas)
                        .RefreshInterval(Time.Parse(schema.RefreshInterval))
                        .Add(schema.Settings))
                    .Mappings(m => m.Properties(schema.Mappings))
                );

                if (response.IsValidResponse && schema.Aliases.Any())
                {
                    foreach (var alias in schema.Aliases)
                    {
                        await CreateAliasAsync(indexName, alias);
                    }
                }

                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdateIndexMappingAsync(string indexName, Dictionary<string, IProperty> mappings)
        {
            try
            {
                var response = await _client.Indices.PutMappingAsync(indexName, m => m
                    .Properties(mappings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteIndexAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.DeleteAsync(indexName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> IndexExistsAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.ExistsAsync(indexName);
                return response.Exists;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<IndexStats> GetIndexStatsAsync(string indexName)
        {
            try
            {
                var statsResponse = await _client.Indices.StatsAsync(indexName);
                var healthResponse = await _client.Cluster.HealthAsync(h => h.Index(indexName));

                if (statsResponse.IsValidResponse && healthResponse.IsValidResponse)
                {
                    var indexStat = statsResponse.Indices?.FirstOrDefault();
                    return new IndexStats
                    {
                        IndexName = indexName,
                        DocumentCount = indexStat.Value?.Total?.Docs?.Count ?? 0,
                        StoreSize = indexStat.Value?.Total?.Store?.SizeInBytes?.ToString() ?? "0",
                        PrimaryShards = indexStat.Value?.Primaries?.Docs?.Count > 0 ? 1 : 0,
                        ReplicaShards = 0,
                        Health = healthResponse.Status?.ToString() ?? "unknown",
                        Status = healthResponse.Status?.ToString() ?? "unknown"
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new IndexStats { IndexName = indexName };
        }

        public async Task<List<IndexStats>> GetAllIndicesStatsAsync()
        {
            try
            {
                var response = await _client.Indices.StatsAsync();
                var stats = new List<IndexStats>();

                if (response.IsValidResponse && response.Indices != null)
                {
                    foreach (var index in response.Indices)
                    {
                        stats.Add(new IndexStats
                        {
                            IndexName = index.Key,
                            DocumentCount = index.Value?.Total?.Docs?.Count ?? 0,
                            StoreSize = index.Value?.Total?.Store?.SizeInBytes?.ToString() ?? "0",
                            Health = "unknown"
                        });
                    }
                }

                return stats;
            }
            catch (Exception)
            {
                return new List<IndexStats>();
            }
        }

        public async Task<bool> CreateAliasAsync(string indexName, string aliasName)
        {
            try
            {
                var response = await _client.Indices.PutAliasAsync(indexName, aliasName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteAliasAsync(string indexName, string aliasName)
        {
            try
            {
                var response = await _client.Indices.DeleteAliasAsync(indexName, aliasName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, List<string>>> GetAliasesAsync()
        {
            try
            {
                var response = await _client.Indices.GetAliasAsync();
                var aliases = new Dictionary<string, List<string>>();

                if (response.IsValidResponse && response.Indices != null)
                {
                    foreach (var index in response.Indices)
                    {
                        var indexAliases = index.Value?.Aliases?.Keys.ToList() ?? new List<string>();
                        aliases[index.Key] = indexAliases;
                    }
                }

                return aliases;
            }
            catch (Exception)
            {
                return new Dictionary<string, List<string>>();
            }
        }

        public async Task<bool> SwitchAliasAsync(string aliasName, string oldIndexName, string newIndexName)
        {
            try
            {
                var response = await _client.Indices.UpdateAliasesAsync(u => u
                    .Actions(a => a
                        .Remove(r => r.Index(oldIndexName).Alias(aliasName))
                        .Add(add => add.Index(newIndexName).Alias(aliasName))));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<ClusterHealth> GetClusterHealthAsync()
        {
            try
            {
                var response = await _client.Cluster.HealthAsync();
                if (response.IsValidResponse)
                {
                    return new ClusterHealth
                    {
                        Status = response.Status?.ToString() ?? "unknown",
                        NumberOfNodes = response.NumberOfNodes,
                        NumberOfDataNodes = response.NumberOfDataNodes,
                        ActivePrimaryShards = response.ActivePrimaryShards,
                        ActiveShards = response.ActiveShards,
                        RelocatingShards = response.RelocatingShards,
                        InitializingShards = response.InitializingShards,
                        UnassignedShards = response.UnassignedShards,
                        ActiveShardsPercentAsNumber = response.ActiveShardsPercentAsNumber,
                        TimedOut = response.TimedOut
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new ClusterHealth();
        }

        public async Task<bool> CreateIndexTemplateAsync(IndexTemplate template)
        {
            try
            {
                var response = await _client.Indices.PutIndexTemplateAsync(template.Name, t => t
                    .IndexPatterns(template.IndexPatterns)
                    .Priority(template.Priority)
                    .Template(tmpl => tmpl
                        .Settings(s => s
                            .NumberOfShards(template.Template.NumberOfShards)
                            .NumberOfReplicas(template.Template.NumberOfReplicas)
                            .Add(template.Template.Settings))
                        .Mappings(m => m.Properties(template.Template.Mappings)))
                    .Meta(template.Metadata));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteIndexTemplateAsync(string templateName)
        {
            try
            {
                var response = await _client.Indices.DeleteIndexTemplateAsync(templateName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<IndexTemplate>> GetIndexTemplatesAsync()
        {
            try
            {
                var response = await _client.Indices.GetIndexTemplateAsync();
                var templates = new List<IndexTemplate>();

                if (response.IsValidResponse && response.IndexTemplates != null)
                {
                    foreach (var template in response.IndexTemplates)
                    {
                        templates.Add(new IndexTemplate
                        {
                            Name = template.Name,
                            IndexPatterns = template.IndexTemplate?.IndexPatterns?.ToList() ?? new List<string>(),
                            Priority = template.IndexTemplate?.Priority ?? 1
                        });
                    }
                }

                return templates;
            }
            catch (Exception)
            {
                return new List<IndexTemplate>();
            }
        }

        public async Task<Dictionary<string, IProperty>> GetIndexMappingAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.GetMappingAsync(indexName);
                if (response.IsValidResponse && response.Indices != null)
                {
                    var indexMapping = response.Indices.FirstOrDefault();
                    return indexMapping.Value?.Mappings?.Properties ?? new Dictionary<string, IProperty>();
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new Dictionary<string, IProperty>();
        }

        public async Task<bool> CreateAnalyzerAsync(string indexName, AnalyzerConfiguration analyzer)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["analysis"] = new Dictionary<string, object>
                    {
                        ["analyzer"] = new Dictionary<string, object>
                        {
                            [analyzer.Name] = new Dictionary<string, object>
                            {
                                ["type"] = analyzer.Type,
                                ["tokenizer"] = analyzer.Tokenizer,
                                ["filter"] = analyzer.Filters
                            }
                        }
                    }
                };

                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdateAnalyzerAsync(string indexName, AnalyzerConfiguration analyzer)
        {
            // Close index first, update settings, then reopen
            try
            {
                await _client.Indices.CloseAsync(indexName);
                var result = await CreateAnalyzerAsync(indexName, analyzer);
                await _client.Indices.OpenAsync(indexName);
                return result;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<AnalyzerConfiguration>> GetAnalyzersAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.GetSettingsAsync(indexName);
                var analyzers = new List<AnalyzerConfiguration>();

                if (response.IsValidResponse && response.Indices != null)
                {
                    // Extract analyzer configurations from settings
                    // This is a simplified implementation
                }

                return analyzers;
            }
            catch (Exception)
            {
                return new List<AnalyzerConfiguration>();
            }
        }

        public async Task<bool> UpdateIndexSettingsAsync(string indexName, Dictionary<string, object> settings)
        {
            try
            {
                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetIndexSettingsAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.GetSettingsAsync(indexName);
                if (response.IsValidResponse && response.Indices != null)
                {
                    var indexSettings = response.Indices.FirstOrDefault();
                    return indexSettings.Value?.Settings?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                           ?? new Dictionary<string, object>();
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new Dictionary<string, object>();
        }

        public async Task<bool> ConfigureShardsAsync(string indexName, ShardConfiguration config)
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["number_of_replicas"] = config.NumberOfReplicas
                };

                if (!string.IsNullOrEmpty(config.RoutingKey))
                {
                    settings["routing.allocation.require._name"] = config.RoutingKey;
                }

                foreach (var setting in config.AllocationSettings)
                {
                    settings[setting.Key] = setting.Value;
                }

                var response = await _client.Indices.PutSettingsAsync(indexName, s => s
                    .Settings(settings));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<ShardConfiguration> GetShardConfigurationAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.GetSettingsAsync(indexName);
                if (response.IsValidResponse && response.Indices != null)
                {
                    var indexSettings = response.Indices.FirstOrDefault();
                    var settings = indexSettings.Value?.Settings;

                    return new ShardConfiguration
                    {
                        NumberOfShards = 1, // Cannot be changed after index creation
                        NumberOfReplicas = settings?.NumberOfReplicas ?? 1,
                        RoutingKey = string.Empty // Extract from settings if available
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new ShardConfiguration();
        }

        public async Task<bool> RebalanceShardsAsync(string indexName)
        {
            try
            {
                var response = await _client.Cluster.RerouteAsync(r => r
                    .RetryFailed(true));
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetShardAllocationAsync()
        {
            try
            {
                var response = await _client.Cluster.AllocationExplainAsync();
                if (response.IsValidResponse)
                {
                    return new Dictionary<string, object>
                    {
                        ["current_state"] = response.CurrentState,
                        ["can_allocate"] = response.CanAllocate,
                        ["can_remain_on_current_node"] = response.CanRemainOnCurrentNode
                    };
                }
            }
            catch (Exception)
            {
                // Handle exception
            }

            return new Dictionary<string, object>();
        }

        public async Task<Dictionary<string, object>> GetNodeStatsAsync()
        {
            try
            {
                var response = await _client.Nodes.StatsAsync();
                var nodeStats = new Dictionary<string, object>();

                if (response.IsValidResponse && response.Nodes != null)
                {
                    foreach (var node in response.Nodes)
                    {
                        nodeStats[node.Key] = new
                        {
                            name = node.Value.Name,
                            host = node.Value.Host,
                            roles = node.Value.Roles,
                            indices = new
                            {
                                docs = node.Value.Indices?.Docs,
                                store = node.Value.Indices?.Store
                            }
                        };
                    }
                }

                return nodeStats;
            }
            catch (Exception)
            {
                return new Dictionary<string, object>();
            }
        }

        public async Task<bool> RefreshIndexAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.RefreshAsync(indexName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> FlushIndexAsync(string indexName)
        {
            try
            {
                var response = await _client.Indices.FlushAsync(indexName);
                return response.IsValidResponse;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
