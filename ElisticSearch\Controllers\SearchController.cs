using ElisticSearch.Models;
using ElisticSearch.Services;
using Microsoft.AspNetCore.Mvc;

namespace ElisticSearch.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SearchController : ControllerBase
    {
        private readonly IAdvancedSearchService _searchService;
        private readonly ILogger<SearchController> _logger;

        public SearchController(IAdvancedSearchService searchService, ILogger<SearchController> logger)
        {
            _searchService = searchService;
            _logger = logger;
        }

        [HttpPost("basic/{indexName}")]
        public async Task<IActionResult> BasicSearch(string indexName, [FromBody] SearchRequest request)
        {
            try
            {
                var result = await _searchService.SearchAsync<User>(indexName, request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing basic search on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("fulltext/{indexName}")]
        public async Task<IActionResult> FullTextSearch(string indexName, [FromQuery] string query, [FromQuery] int from = 0, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.FullTextSearchAsync<User>(indexName, query, from, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing full-text search on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("match/{indexName}")]
        public async Task<IActionResult> MatchQuery(string indexName, [FromQuery] string field, [FromQuery] string value, [FromQuery] int from = 0, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.MatchQueryAsync<User>(indexName, field, value, from, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing match query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("term/{indexName}")]
        public async Task<IActionResult> TermQuery(string indexName, [FromQuery] string field, [FromQuery] string value, [FromQuery] int from = 0, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.TermQueryAsync<User>(indexName, field, value, from, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing term query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("bool/{indexName}")]
        public async Task<IActionResult> BoolQuery(string indexName, [FromBody] BoolQueryRequest request)
        {
            try
            {
                var result = await _searchService.BoolQueryAsync<User>(indexName, request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bool query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("range/{indexName}")]
        public async Task<IActionResult> RangeQuery(string indexName, [FromQuery] string field, [FromQuery] string from, [FromQuery] string to, [FromQuery] int skip = 0, [FromQuery] int take = 10)
        {
            try
            {
                var result = await _searchService.RangeQueryAsync<User>(indexName, field, from, to, skip, take);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing range query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("wildcard/{indexName}")]
        public async Task<IActionResult> WildcardQuery(string indexName, [FromQuery] string field, [FromQuery] string pattern, [FromQuery] int from = 0, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.WildcardQueryAsync<User>(indexName, field, pattern, from, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing wildcard query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("fuzzy/{indexName}")]
        public async Task<IActionResult> FuzzyQuery(string indexName, [FromQuery] string field, [FromQuery] string value, [FromQuery] int fuzziness = 1, [FromQuery] int from = 0, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.FuzzyQueryAsync<User>(indexName, field, value, fuzziness, from, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing fuzzy query on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("filtered/{indexName}")]
        public async Task<IActionResult> FilteredSearch(string indexName, [FromBody] FilteredSearchRequest request)
        {
            try
            {
                var result = await _searchService.FilteredSearchAsync<User>(indexName, request.SearchRequest, request.Filters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing filtered search on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("multifield/{indexName}")]
        public async Task<IActionResult> MultiFieldSearch(string indexName, [FromBody] MultiFieldSearchRequest request)
        {
            try
            {
                var result = await _searchService.MultiFieldSearchAsync<User>(indexName, request.FieldValues, request.From, request.Size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing multi-field search on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("aggregations/{indexName}")]
        public async Task<IActionResult> GetAggregations(string indexName, [FromBody] AggregationSearchRequest request)
        {
            try
            {
                var result = await _searchService.GetAggregationsAsync(indexName, request.Aggregations, request.SearchRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting aggregations for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("aggregations/terms/{indexName}")]
        public async Task<IActionResult> GetTermsAggregation(string indexName, [FromQuery] string field, [FromQuery] int size = 10)
        {
            try
            {
                var result = await _searchService.GetTermsAggregationAsync(indexName, field, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting terms aggregation for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("aggregations/date-histogram/{indexName}")]
        public async Task<IActionResult> GetDateHistogram(string indexName, [FromQuery] string field, [FromQuery] string interval)
        {
            try
            {
                var result = await _searchService.GetDateHistogramAsync(indexName, field, interval);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting date histogram for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("aggregations/stats/{indexName}")]
        public async Task<IActionResult> GetStatsAggregation(string indexName, [FromQuery] string field)
        {
            try
            {
                var result = await _searchService.GetStatsAggregationAsync(indexName, field);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stats aggregation for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("suggestions/{indexName}")]
        public async Task<IActionResult> GetSuggestions(string indexName, [FromQuery] string text, [FromQuery] string field, [FromQuery] int size = 5)
        {
            try
            {
                var result = await _searchService.GetSuggestionsAsync(indexName, text, field, size);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting suggestions for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("highlight/{indexName}")]
        public async Task<IActionResult> SearchWithHighlight(string indexName, [FromBody] HighlightSearchRequest request)
        {
            try
            {
                var result = await _searchService.SearchWithHighlightAsync<User>(indexName, request.SearchRequest, request.Highlight);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search with highlight on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("sort/{indexName}")]
        public async Task<IActionResult> SearchWithSort(string indexName, [FromBody] SortSearchRequest request)
        {
            try
            {
                var result = await _searchService.SearchWithSortAsync<User>(indexName, request.SearchRequest, request.Sorts);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search with sort on index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("scroll/initiate/{indexName}")]
        public async Task<IActionResult> InitiateScroll(string indexName, [FromBody] ScrollInitiateRequest request)
        {
            try
            {
                var result = await _searchService.InitiateScrollAsync<User>(indexName, request.SearchRequest, request.ScrollTime);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating scroll for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("scroll/continue")]
        public async Task<IActionResult> ContinueScroll([FromBody] ScrollContinueRequest request)
        {
            try
            {
                var result = await _searchService.ContinueScrollAsync<User>(request.ScrollId, request.ScrollTime);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error continuing scroll with ID {ScrollId}", request.ScrollId);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpDelete("scroll/{scrollId}")]
        public async Task<IActionResult> ClearScroll(string scrollId)
        {
            try
            {
                var result = await _searchService.ClearScrollAsync(scrollId);
                return result ? Ok("Scroll cleared successfully.") 
                             : StatusCode(500, "Failed to clear scroll.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing scroll with ID {ScrollId}", scrollId);
                return StatusCode(500, "Internal server error.");
            }
        }
    }

    // Request DTOs
    public class FilteredSearchRequest
    {
        public SearchRequest SearchRequest { get; set; } = new();
        public List<FilterRequest> Filters { get; set; } = new();
    }

    public class MultiFieldSearchRequest
    {
        public Dictionary<string, object> FieldValues { get; set; } = new();
        public int From { get; set; } = 0;
        public int Size { get; set; } = 10;
    }

    public class AggregationSearchRequest
    {
        public List<AggregationRequest> Aggregations { get; set; } = new();
        public SearchRequest? SearchRequest { get; set; }
    }

    public class HighlightSearchRequest
    {
        public SearchRequest SearchRequest { get; set; } = new();
        public HighlightRequest Highlight { get; set; } = new();
    }

    public class SortSearchRequest
    {
        public SearchRequest SearchRequest { get; set; } = new();
        public List<SortRequest> Sorts { get; set; } = new();
    }

    public class ScrollInitiateRequest
    {
        public SearchRequest SearchRequest { get; set; } = new();
        public string ScrollTime { get; set; } = "1m";
    }

    public class ScrollContinueRequest
    {
        public string ScrollId { get; set; } = string.Empty;
        public string ScrollTime { get; set; } = "1m";
    }
}
