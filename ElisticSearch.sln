﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElisticSearch", "ElisticSearch\ElisticSearch.csproj", "{34863DF7-7FC5-46F5-8BF1-0BDBD2F57181}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{34863DF7-7FC5-46F5-8BF1-0BDBD2F57181}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34863DF7-7FC5-46F5-8BF1-0BDBD2F57181}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34863DF7-7FC5-46F5-8BF1-0BDBD2F57181}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34863DF7-7FC5-46F5-8BF1-0BDBD2F57181}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EB6F2A91-B947-41E1-9810-606B84E0BEF4}
	EndGlobalSection
EndGlobal
