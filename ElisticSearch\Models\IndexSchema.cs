using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Elastic.Clients.Elasticsearch.Mapping;

namespace ElisticSearch.Models
{
    public class IndexSchema
    {
        public string IndexName { get; set; } = string.Empty;
        public int NumberOfShards { get; set; } = 1;
        public int NumberOfReplicas { get; set; } = 1;
        public string RefreshInterval { get; set; } = "1s";
        public Dictionary<string, object> Settings { get; set; } = new();
        public Dictionary<string, IProperty> Mappings { get; set; } = new();
        public List<string> Aliases { get; set; } = new();
    }

    public class ShardConfiguration
    {
        public int NumberOfShards { get; set; } = 1;
        public int NumberOfReplicas { get; set; } = 1;
        public string RoutingKey { get; set; } = string.Empty;
        public Dictionary<string, object> AllocationSettings { get; set; } = new();
    }

    public class IndexTemplate
    {
        public string Name { get; set; } = string.Empty;
        public List<string> IndexPatterns { get; set; } = new();
        public int Priority { get; set; } = 1;
        public IndexSchema Template { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class AnalyzerConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> Settings { get; set; } = new();
        public List<string> Filters { get; set; } = new();
        public string Tokenizer { get; set; } = string.Empty;
    }

    public class SearchResult<T>
    {
        public long Total { get; set; }
        public List<T> Documents { get; set; } = new();
        public Dictionary<string, object> Aggregations { get; set; } = new();
        public double MaxScore { get; set; }
        public TimeSpan Took { get; set; }
    }

    public class SearchRequest
    {
        public string Query { get; set; } = string.Empty;
        public int From { get; set; } = 0;
        public int Size { get; set; } = 10;
        public List<string> Fields { get; set; } = new();
        public Dictionary<string, object> Filters { get; set; } = new();
        public Dictionary<string, object> Sort { get; set; } = new();
        public Dictionary<string, object> Aggregations { get; set; } = new();
        public bool IncludeHighlight { get; set; } = false;
    }

    public class IndexStats
    {
        public string IndexName { get; set; } = string.Empty;
        public long DocumentCount { get; set; }
        public string StoreSize { get; set; } = string.Empty;
        public int PrimaryShards { get; set; }
        public int ReplicaShards { get; set; }
        public string Health { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class ClusterHealth
    {
        public string Status { get; set; } = string.Empty;
        public int NumberOfNodes { get; set; }
        public int NumberOfDataNodes { get; set; }
        public int ActivePrimaryShards { get; set; }
        public int ActiveShards { get; set; }
        public int RelocatingShards { get; set; }
        public int InitializingShards { get; set; }
        public int UnassignedShards { get; set; }
        public double ActiveShardsPercentAsNumber { get; set; }
        public bool TimedOut { get; set; }
    }
}
