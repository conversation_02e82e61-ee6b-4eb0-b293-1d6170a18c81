using ElisticSearch.Models;
using ElisticSearch.Services;
using Microsoft.AspNetCore.Mvc;

namespace ElisticSearch.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ShardController : ControllerBase
    {
        private readonly IShardManagementService _shardService;
        private readonly ILogger<ShardController> _logger;

        public ShardController(IShardManagementService shardService, ILogger<ShardController> logger)
        {
            _shardService = shardService;
            _logger = logger;
        }

        [HttpPost("configure/{indexName}")]
        public async Task<IActionResult> ConfigureShards(string indexName, [FromBody] ShardConfigurationRequest request)
        {
            try
            {
                var result = await _shardService.ConfigureIndexShardsAsync(indexName, request.NumberOfShards, request.NumberOfReplicas);
                return result ? Ok($"Shard configuration updated for index {indexName}.") 
                             : StatusCode(500, "Failed to configure shards.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring shards for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPut("replicas/{indexName}")]
        public async Task<IActionResult> UpdateReplicas(string indexName, [FromBody] UpdateReplicasRequest request)
        {
            try
            {
                var result = await _shardService.UpdateReplicaCountAsync(indexName, request.NumberOfReplicas);
                return result ? Ok($"Replica count updated to {request.NumberOfReplicas} for index {indexName}.") 
                             : StatusCode(500, "Failed to update replica count.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating replicas for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("info/{indexName}")]
        public async Task<IActionResult> GetShardInfo(string indexName)
        {
            try
            {
                var info = await _shardService.GetShardInfoAsync(indexName);
                return Ok(info);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shard info for index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("info")]
        public async Task<IActionResult> GetAllShardsInfo()
        {
            try
            {
                var info = await _shardService.GetAllShardsInfoAsync();
                return Ok(info);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all shards info");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetShardStatus()
        {
            try
            {
                var status = await _shardService.GetShardStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shard status");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("unassigned")]
        public async Task<IActionResult> GetUnassignedShards()
        {
            try
            {
                var unassigned = await _shardService.GetUnassignedShardsAsync();
                return Ok(unassigned);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unassigned shards");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("relocating")]
        public async Task<IActionResult> GetRelocatingShards()
        {
            try
            {
                var relocating = await _shardService.GetRelocatingShardsAsync();
                return Ok(relocating);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting relocating shards");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("rebalance")]
        public async Task<IActionResult> RebalanceCluster()
        {
            try
            {
                var result = await _shardService.RebalanceClusterAsync();
                return result ? Ok("Cluster rebalancing initiated.") 
                             : StatusCode(500, "Failed to initiate cluster rebalancing.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rebalancing cluster");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("rebalance/{indexName}")]
        public async Task<IActionResult> RebalanceIndex(string indexName)
        {
            try
            {
                var result = await _shardService.RebalanceIndexAsync(indexName);
                return result ? Ok($"Index {indexName} rebalancing initiated.") 
                             : StatusCode(500, "Failed to initiate index rebalancing.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rebalancing index {IndexName}", indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("move")]
        public async Task<IActionResult> MoveShard([FromBody] MoveShardRequest request)
        {
            try
            {
                var result = await _shardService.MoveShardAsync(request.IndexName, request.ShardNumber, request.FromNode, request.ToNode);
                return result ? Ok($"Shard {request.ShardNumber} move initiated from {request.FromNode} to {request.ToNode}.") 
                             : StatusCode(500, "Failed to move shard.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving shard {ShardNumber} for index {IndexName}", 
                    request.ShardNumber, request.IndexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("cancel-move")]
        public async Task<IActionResult> CancelShardMovement([FromBody] CancelShardMovementRequest request)
        {
            try
            {
                var result = await _shardService.CancelShardMovementAsync(request.IndexName, request.ShardNumber);
                return result ? Ok($"Shard {request.ShardNumber} movement cancelled for index {request.IndexName}.") 
                             : StatusCode(500, "Failed to cancel shard movement.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling shard movement for shard {ShardNumber} in index {IndexName}", 
                    request.ShardNumber, request.IndexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("allocation/explain/{indexName}/{shardNumber}")]
        public async Task<IActionResult> ExplainShardAllocation(string indexName, int shardNumber)
        {
            try
            {
                var explanation = await _shardService.ExplainShardAllocationAsync(indexName, shardNumber);
                return Ok(explanation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error explaining shard allocation for shard {ShardNumber} in index {IndexName}", 
                    shardNumber, indexName);
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("nodes")]
        public async Task<IActionResult> GetNodesInfo()
        {
            try
            {
                var nodes = await _shardService.GetNodesInfoAsync();
                return Ok(nodes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nodes info");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("allocation/enable")]
        public async Task<IActionResult> EnableShardAllocation()
        {
            try
            {
                var result = await _shardService.EnableShardAllocationAsync();
                return result ? Ok("Shard allocation enabled.") 
                             : StatusCode(500, "Failed to enable shard allocation.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling shard allocation");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpPost("allocation/disable")]
        public async Task<IActionResult> DisableShardAllocation()
        {
            try
            {
                var result = await _shardService.DisableShardAllocationAsync();
                return result ? Ok("Shard allocation disabled.") 
                             : StatusCode(500, "Failed to disable shard allocation.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disabling shard allocation");
                return StatusCode(500, "Internal server error.");
            }
        }

        [HttpGet("allocation/settings")]
        public async Task<IActionResult> GetShardAllocationSettings()
        {
            try
            {
                var settings = await _shardService.GetShardAllocationSettingsAsync();
                return Ok(settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shard allocation settings");
                return StatusCode(500, "Internal server error.");
            }
        }
    }

    // Request DTOs
    public class ShardConfigurationRequest
    {
        public int NumberOfShards { get; set; } = 1;
        public int NumberOfReplicas { get; set; } = 1;
    }

    public class UpdateReplicasRequest
    {
        public int NumberOfReplicas { get; set; } = 1;
    }

    public class MoveShardRequest
    {
        public string IndexName { get; set; } = string.Empty;
        public int ShardNumber { get; set; }
        public string FromNode { get; set; } = string.Empty;
        public string ToNode { get; set; } = string.Empty;
    }

    public class CancelShardMovementRequest
    {
        public string IndexName { get; set; } = string.Empty;
        public int ShardNumber { get; set; }
    }
}
