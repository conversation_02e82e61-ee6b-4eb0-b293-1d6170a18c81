﻿using ElisticSearch.Models;
using ElisticSearch.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace ElisticSearch.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly ILogger<UsersController> _logger;
        private readonly IElasticService _elasticService;
        public UsersController(ILogger<UsersController> logger, IElasticService elasticService)
        {
            _logger = logger;
            _elasticService = elasticService;
        }
        [HttpPost("create-index")]
        public async Task<IActionResult> CreateIndex(string indexName)
        {
            await _elasticService.CreateIndexIfNotExistsAsync(indexName);
            return Ok($"Index {indexName} created or already exists.");
        }
        [HttpPost("add-user")]
        public async Task<IActionResult> AddUser([FromBody] User user)
        {
            var result = await _elasticService.AddOrUpdate(user);
            return result ? Ok("User added or updated Successfully.")
                : StatusCode(500, "Error adding or updating user.");
        }
        [HttpGet("get-user/{key}")]
        public async Task<IActionResult> GetUser(string key)
        {
            var user = await _elasticService.Get(key);
            return user != null ? Ok(user) : NotFound("User not found");
        }
        [HttpGet("get-all-users")]
        public async Task<IActionResult> GetAllUsers()
        {
            var users = await _elasticService.GetAll();
            return users != null ? Ok(users) : StatusCode(500, "Error retrieving users");
        }
        [HttpGet("delete-user/{key}")]
        public async Task<IActionResult> DeleteUser(string key)
        {
            var result = await _elasticService.Remove(key);
            return result ? Ok("User deleted successfully.") : StatusCode(500, "Error deleting user.");
        }
    }
}
