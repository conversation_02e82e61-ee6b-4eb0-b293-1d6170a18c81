﻿//using Elastic.Clients.Elasticsearch;

//namespace ElisticSearch.Services
//{
//    public class ProductSearchService : IProductSearchService
//    {
//        private readonly ElasticsearchClient _client;
//        private const string IndexName = "products";

//        public ProductSearchService(ElasticsearchClient client)
//        {
//            _client = client;
//        }

//        // Ensure index exists with mappings (run at startup)
//        public async Task EnsureIndexAsync()
//        {
//            var exists = await _client.Indices.ExistsAsync(IndexName);
//            if (!exists.Exists)
//            {
//                await _client.Indices.CreateAsync(IndexName, c => c
//                    .Mappings<ProductDocument>(m => m
//                        .Properties(ps => ps
//                            .Text(t => t.Name(n => n.Name).Analyzer("standard"))
//                            .Number(nu => nu.Name(n => n.Price).Type(NumberType.Double))
//                            .Keyword(k => k.Name(n => n.Category))
//                        )
//                    )
//                );
//            }
//        }

//        public async Task IndexAsync(ProductDocument doc)
//        {
//            await _client.IndexAsync(doc, i => i.Index(IndexName).Id(doc.Id));
//        }

//        public async Task BulkIndexAsync(IEnumerable<ProductDocument> docs)
//        {
//            var response = await _client.BulkAsync(b => b
//                .Index(IndexName)
//                .Operations(op =>
//                {
//                    foreach (var d in docs)
//                        op.Index<ProductDocument>(idx => idx.Document(d).Id(d.Id));
//                    return op;
//                })
//            );
//            if (response.Errors)
//                throw new Exception("Bulk indexing encountered errors.");
//        }

//        public async Task DeleteAsync(string id) =>
//            await _client.DeleteAsync(new DeleteRequest(IndexName, id));

//        public async Task<SearchResult<ProductDocument>> SearchAsync(string query, int skip = 0, int take = 10)
//        {
//            var resp = await _client.SearchAsync<ProductDocument>(s => s
//                .Index(IndexName)
//                .Query(q => q
//                    .MultiMatch(m => m
//                        .Query(query)
//                        .Fields(f => f.Field(ff => ff.Name).Field(ff => ff.Description))
//                        .Fuzziness("AUTO")
//                    )
//                )
//                .From(skip)
//                .Size(take)
//            );

//            return new SearchResult<ProductDocument>
//            {
//                Total = resp.Total?.Value ?? 0,
//                Documents = resp.Hits.Select(h => h.Source!).ToList()
//            };
//        }
//    }

//    // Simple DTOs used above
//    public class ProductDocument
//    {
//        public string Id { get; set; } = default!;
//        public string Name { get; set; } = default!;
//        public string? Description { get; set; }
//        public double Price { get; set; }
//        public string? Category { get; set; }
//    }

//    public class SearchResult<T>
//    {
//        public long Total { get; set; }
//        public List<T> Documents { get; set; } = new();
//    }
//}
