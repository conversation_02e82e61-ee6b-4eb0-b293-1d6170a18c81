using Elastic.Clients.Elasticsearch;
using ElisticSearch.Configuration;
using ElisticSearch.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.


// Program.cs
//var esUrl = builder.Configuration["Elasticsearch:Url"]; // e.g. http://localhost:9200

//var settings = new ElasticsearchClientSettings(new Uri(esUrl))
//    .DefaultIndex("products");

// Optional: set basic auth or API key
// .ApiKey(new ApiKeyAuthentication("id","apiKey")) or .Authentication(new BasicAuthentication("user","pass"))

//var esClient = new ElasticsearchClient(settings);
//builder.Services.AddSingleton(esClient);

// Register your implementation
//builder.Services.AddScoped<IProductSearchService, ProductSearchService>();

// Ensure index on startup
//var sp = builder.Services.BuildServiceProvider();
//var searchSvc = sp.GetRequiredService<IProductSearchService>() as ProductSearchService;
//await searchSvc!.EnsureIndexAsync();



builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.Configure<ElasticsSettings>(
    builder.Configuration.GetSection("ElasticsSettings"));

builder.Services.AddSingleton<IElasticService, ElasticService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();





