using ElisticSearch.Models;

namespace ElisticSearch.Services
{
    public interface IMonitoringService
    {
        // Cluster Health
        Task<ClusterHealth> GetClusterHealthAsync();
        Task<ClusterStats> GetClusterStatsAsync();
        Task<List<NodeHealth>> GetNodesHealthAsync();
        Task<bool> IsClusterHealthyAsync();

        // Index Monitoring
        Task<List<IndexStats>> GetAllIndicesStatsAsync();
        Task<IndexStats> GetIndexStatsAsync(string indexName);
        Task<IndexHealth> GetIndexHealthAsync(string indexName);
        Task<List<IndexPerformanceMetrics>> GetIndexPerformanceMetricsAsync();

        // Node Monitoring
        Task<List<NodeStats>> GetNodesStatsAsync();
        Task<NodeStats> GetNodeStatsAsync(string nodeId);
        Task<NodeInfo> GetNodeInfoAsync(string nodeId);
        Task<List<NodePerformanceMetrics>> GetNodesPerformanceAsync();

        // Performance Metrics
        Task<PerformanceMetrics> GetPerformanceMetricsAsync();
        Task<List<QueryPerformanceMetrics>> GetQueryPerformanceAsync();
        Task<List<IndexingPerformanceMetrics>> GetIndexingPerformanceAsync();
        Task<SearchLatencyMetrics> GetSearchLatencyMetricsAsync();

        // Resource Monitoring
        Task<ResourceUsage> GetResourceUsageAsync();
        Task<List<DiskUsage>> GetDiskUsageAsync();
        Task<MemoryUsage> GetMemoryUsageAsync();
        Task<CpuUsage> GetCpuUsageAsync();

        // Alerts and Thresholds
        Task<List<Alert>> GetActiveAlertsAsync();
        Task<bool> SetThresholdAsync(string metricName, double warningThreshold, double criticalThreshold);
        Task<List<Threshold>> GetThresholdsAsync();
        Task<bool> CheckThresholdsAsync();

        // Historical Data
        Task<List<HistoricalMetric>> GetHistoricalMetricsAsync(string metricName, DateTime from, DateTime to);
        Task<ClusterTrend> GetClusterTrendAsync(TimeSpan period);
        Task<List<IndexTrend>> GetIndexTrendsAsync(TimeSpan period);

        // Logging and Audit
        Task<List<LogEntry>> GetLogsAsync(LogLevel level, DateTime from, DateTime to);
        Task<List<AuditEntry>> GetAuditTrailAsync(DateTime from, DateTime to);
        Task<bool> LogCustomEventAsync(string eventType, string message, Dictionary<string, object> metadata);
    }

    // Supporting Models for Monitoring
    public class ClusterStats
    {
        public string ClusterName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int NumberOfNodes { get; set; }
        public int NumberOfDataNodes { get; set; }
        public long TotalIndices { get; set; }
        public long TotalShards { get; set; }
        public long TotalDocuments { get; set; }
        public string TotalStoreSize { get; set; } = string.Empty;
        public double CpuUsagePercent { get; set; }
        public double MemoryUsagePercent { get; set; }
        public double DiskUsagePercent { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class NodeHealth
    {
        public string NodeId { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public int ActiveShards { get; set; }
        public DateTime LastSeen { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class IndexHealth
    {
        public string IndexName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public int NumberOfShards { get; set; }
        public int NumberOfReplicas { get; set; }
        public int ActiveShards { get; set; }
        public int UnassignedShards { get; set; }
        public long DocumentCount { get; set; }
        public string StoreSize { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdated { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class IndexPerformanceMetrics
    {
        public string IndexName { get; set; } = string.Empty;
        public double IndexingRate { get; set; }
        public double SearchRate { get; set; }
        public double AverageIndexingLatency { get; set; }
        public double AverageSearchLatency { get; set; }
        public long TotalIndexingOperations { get; set; }
        public long TotalSearchOperations { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class NodePerformanceMetrics
    {
        public string NodeId { get; set; } = string.Empty;
        public string NodeName { get; set; } = string.Empty;
        public double CpuUsagePercent { get; set; }
        public double MemoryUsagePercent { get; set; }
        public double DiskUsagePercent { get; set; }
        public double NetworkInBytes { get; set; }
        public double NetworkOutBytes { get; set; }
        public int ThreadPoolQueueSize { get; set; }
        public int GarbageCollectionCount { get; set; }
        public double GarbageCollectionTime { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class PerformanceMetrics
    {
        public double OverallCpuUsage { get; set; }
        public double OverallMemoryUsage { get; set; }
        public double OverallDiskUsage { get; set; }
        public double AverageSearchLatency { get; set; }
        public double AverageIndexingLatency { get; set; }
        public long TotalSearches { get; set; }
        public long TotalIndexingOperations { get; set; }
        public double SearchThroughput { get; set; }
        public double IndexingThroughput { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class QueryPerformanceMetrics
    {
        public string QueryType { get; set; } = string.Empty;
        public double AverageLatency { get; set; }
        public double MaxLatency { get; set; }
        public double MinLatency { get; set; }
        public long TotalQueries { get; set; }
        public double QueriesPerSecond { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class IndexingPerformanceMetrics
    {
        public string IndexName { get; set; } = string.Empty;
        public double AverageLatency { get; set; }
        public double MaxLatency { get; set; }
        public double MinLatency { get; set; }
        public long TotalOperations { get; set; }
        public double OperationsPerSecond { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class SearchLatencyMetrics
    {
        public double P50Latency { get; set; }
        public double P95Latency { get; set; }
        public double P99Latency { get; set; }
        public double AverageLatency { get; set; }
        public double MaxLatency { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class ResourceUsage
    {
        public CpuUsage Cpu { get; set; } = new();
        public MemoryUsage Memory { get; set; } = new();
        public List<DiskUsage> Disks { get; set; } = new();
        public NetworkUsage Network { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class DiskUsage
    {
        public string Path { get; set; } = string.Empty;
        public long TotalBytes { get; set; }
        public long UsedBytes { get; set; }
        public long AvailableBytes { get; set; }
        public double UsagePercent { get; set; }
        public string NodeId { get; set; } = string.Empty;
    }

    public class MemoryUsage
    {
        public long TotalBytes { get; set; }
        public long UsedBytes { get; set; }
        public long AvailableBytes { get; set; }
        public double UsagePercent { get; set; }
        public long HeapUsedBytes { get; set; }
        public long HeapMaxBytes { get; set; }
        public double HeapUsagePercent { get; set; }
    }

    public class CpuUsage
    {
        public double UsagePercent { get; set; }
        public double LoadAverage1m { get; set; }
        public double LoadAverage5m { get; set; }
        public double LoadAverage15m { get; set; }
        public int ProcessorCount { get; set; }
    }

    public class NetworkUsage
    {
        public long BytesReceived { get; set; }
        public long BytesSent { get; set; }
        public long PacketsReceived { get; set; }
        public long PacketsSent { get; set; }
        public double ReceiveRate { get; set; }
        public double SendRate { get; set; }
    }

    public class Alert
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class Threshold
    {
        public string MetricName { get; set; } = string.Empty;
        public double WarningThreshold { get; set; }
        public double CriticalThreshold { get; set; }
        public string Unit { get; set; } = string.Empty;
        public bool IsEnabled { get; set; } = true;
    }

    public class HistoricalMetric
    {
        public string MetricName { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Tags { get; set; } = new();
    }

    public class ClusterTrend
    {
        public TimeSpan Period { get; set; }
        public List<TrendPoint> HealthTrend { get; set; } = new();
        public List<TrendPoint> PerformanceTrend { get; set; } = new();
        public List<TrendPoint> ResourceTrend { get; set; } = new();
    }

    public class IndexTrend
    {
        public string IndexName { get; set; } = string.Empty;
        public TimeSpan Period { get; set; }
        public List<TrendPoint> DocumentCountTrend { get; set; } = new();
        public List<TrendPoint> SizeTrend { get; set; } = new();
        public List<TrendPoint> PerformanceTrend { get; set; } = new();
    }

    public class TrendPoint
    {
        public DateTime Timestamp { get; set; }
        public double Value { get; set; }
        public string Label { get; set; } = string.Empty;
    }

    public class LogEntry
    {
        public string Id { get; set; } = string.Empty;
        public LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    public class AuditEntry
    {
        public string Id { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string User { get; set; } = string.Empty;
        public string Resource { get; set; } = string.Empty;
        public string Result { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Details { get; set; } = new();
    }

    public enum LogLevel
    {
        Trace,
        Debug,
        Information,
        Warning,
        Error,
        Critical
    }
}
